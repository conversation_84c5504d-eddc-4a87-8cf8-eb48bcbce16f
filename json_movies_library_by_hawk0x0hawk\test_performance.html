<!DOCTYPE html>
<html>
<head>
    <title>إنشاء ملف JSON كبير للاختبار</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .progress { margin: 20px 0; }
        .download-link { display: none; margin: 20px 0; }
        .download-link a { color: #28a745; text-decoration: none; font-weight: bold; }
    </style>
</head>
<body>
    <h1>إنشاء ملف JSON كبير للاختبار</h1>
    <p>هذه الأداة تنشئ ملف JSON كبير يحتوي على عدة أقسام مع آلاف البطاقات لاختبار أداء التطبيق.</p>

    <button onclick="generateLargeJSON(1000)">إنشاء ملف صغير (1000 عنصر)</button>
    <button onclick="generateLargeJSON(10000)">إنشاء ملف متوسط (10,000 عنصر)</button>
    <button onclick="generateLargeJSON(50000)">إنشاء ملف كبير (50,000 عنصر)</button>
    <button onclick="generateLargeJSON(100000)">إنشاء ملف كبير جداً (100,000 عنصر)</button>

    <div class="progress" id="progress"></div>
    <div class="download-link" id="downloadLink"></div>

    <script>
        function generateLargeJSON(totalItems) {
            const progressDiv = document.getElementById('progress');
            const downloadDiv = document.getElementById('downloadLink');

            progressDiv.innerHTML = 'جاري إنشاء الملف...';
            downloadDiv.style.display = 'none';

            const categories = [
                'action_movies',
                'comedy_movies',
                'drama_movies',
                'horror_movies',
                'sci_fi_movies',
                'romance_movies',
                'thriller_movies',
                'documentary_movies'
            ];

            const categoryNames = {
                'action_movies': 'أفلام الأكشن',
                'comedy_movies': 'أفلام الكوميديا',
                'drama_movies': 'أفلام الدراما',
                'horror_movies': 'أفلام الرعب',
                'sci_fi_movies': 'أفلام الخيال العلمي',
                'romance_movies': 'أفلام الرومانسية',
                'thriller_movies': 'أفلام الإثارة',
                'documentary_movies': 'الأفلام الوثائقية'
            };

            const jsonData = {
                website_info: {
                    name: "مكتبة الأفلام الكبيرة",
                    version: "3.0",
                    total_items: totalItems,
                    created: new Date().toISOString()
                },
                all_movies: []
            };

            const itemsPerCategory = Math.floor(totalItems / categories.length);
            let currentId = 1;

            categories.forEach((category, categoryIndex) => {
                const itemsInThisCategory = categoryIndex === categories.length - 1
                    ? totalItems - (currentId - 1) // Last category gets remaining items
                    : itemsPerCategory;

                for (let i = 0; i < itemsInThisCategory; i++) {
                    const movie = {
                        id: currentId,
                        movies_name: `فيلم ${categoryNames[category]} رقم ${i + 1}`,
                        movies_img: `https://via.placeholder.com/300x400/ff6b6b/ffffff?text=Movie+${currentId}`,
                        movies_href: `https://example.com/movie/${currentId}`,
                        category: category.replace('_movies', ''),
                        year: 2020 + Math.floor(Math.random() * 4),
                        rating: (Math.random() * 4 + 6).toFixed(1),
                        duration: Math.floor(Math.random() * 60 + 90),
                        genre: categoryNames[category]
                    };

                    jsonData.all_movies.push(movie);
                    currentId++;

                    // Update progress
                    if (currentId % 1000 === 0) {
                        const progress = Math.round((currentId / totalItems) * 100);
                        progressDiv.innerHTML = `جاري إنشاء الملف... ${progress}% (${currentId.toLocaleString()}/${totalItems.toLocaleString()})`;
                    }
                }
            });

            // Convert to JSON string
            progressDiv.innerHTML = 'جاري تحويل البيانات إلى JSON...';

            setTimeout(() => {
                try {
                    const jsonString = JSON.stringify(jsonData, null, 2);
                    const blob = new Blob([jsonString], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);

                    const fileName = `large_movies_${totalItems.toLocaleString()}_items.json`;

                    downloadDiv.innerHTML = `
                        <h3>✅ تم إنشاء الملف بنجاح!</h3>
                        <p>الملف يحتوي على ${totalItems.toLocaleString()} عنصر موزعة على ${categories.length} أقسام</p>
                        <a href="${url}" download="${fileName}">تحميل الملف: ${fileName}</a>
                        <p><small>حجم الملف: ${(blob.size / 1024 / 1024).toFixed(2)} ميجابايت</small></p>
                    `;
                    downloadDiv.style.display = 'block';
                    progressDiv.innerHTML = '';

                } catch (error) {
                    progressDiv.innerHTML = `خطأ في إنشاء الملف: ${error.message}`;
                }
            }, 100);
        }
    </script>
</body>
</html>
