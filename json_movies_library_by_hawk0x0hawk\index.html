<html>
<head>
    <meta charset="UTF-8">
    <title>JSON Movies Library</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            padding: 20px;
        }

        .container {
            width: 100vw;
            max-width: 100vw;
            margin: 0;
            padding: 0;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
            width: 100vw;
            max-width: 100vw;
            padding: 0 2vw;
            box-sizing: border-box;
        }

        .card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.3s;
            display: flex;
            flex-direction: column;
            height: 400px; /* Fixed height for uniformity */
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card img {
            width: 100%;
            height: 280px; /* Fixed height for uniformity */
            object-fit: cover;
        }

        .card-content {
            padding: 15px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .card-title {
            font-size: 1.15em;
            margin-bottom: 10px;
            color: #333;
            height: 70px; /* Increased height for longer titles */
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            line-clamp: 3;
        }

        .card-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: auto;
            gap: 5px;
        }

        .card-link {
            padding: 8px 20px;
            background: #dc3545;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 0.95em;
            order: 2;
        }

        .card-link:hover {
            background: #c82333;
        }

        .edit-btn {
            background: #6c757d;
            padding: 6px 15px;
            font-size: 0.85em;
            order: 1;
        }

        .edit-btn:hover {
            background: #5a6268;
        }

        .delete-btn {
            background: #dc3545;
            padding: 6px 10px;
            font-size: 1em;
            order: 0;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            min-width: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .delete-btn:hover {
            background: #c82333;
            transform: scale(1.1);
        }

        .controls {
            margin-bottom: 20px;
        }

        #jsonInput {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        button {
            padding: 10px 20px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }

        button:hover {
            background: #218838;
        }

        .error {
            color: red;
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            transition: all 0.3s ease;
        }

        .error:empty {
            display: none;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: #6c757d;
        }

        .upload-btn:hover {
            background: #5a6268;
        }

        .large-file-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 1em;
        }

        .large-file-btn:hover {
            background: #138496;
        }

        .edit-form {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.3);
            z-index: 1000;
            display: none;
            width: 90%;
            max-width: 500px;
        }

        .edit-form input {
            width: 100%;
            padding: 8px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .edit-form-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 15px;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            z-index: 999;
        }

        .category-filters {
            margin: 20px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .category-btn {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .category-btn.active {
            background: #0056b3;
        }

        .stats {
            margin: 10px 0;
            color: #666;
        }

        /* Add new styles for progress bar */
        .progress-container {
            display: none;
            margin: 10px 0;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-bar {
            width: 0%;
            height: 20px;
            background: #28a745;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            margin-top: 5px;
            color: #666;
        }

        /* Add new styles for category sections */
        .category-section {
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .category-header {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            margin-bottom: 15px;
        }

        .category-header:hover {
            background: #e9ecef;
        }

        .category-header h2 {
            margin: 0;
            font-size: 1.4em;
            color: #333;
        }

        .category-header .movie-count {
            margin-left: 15px;
            color: #6c757d;
            font-size: 0.9em;
        }

        .category-header .toggle-icon {
            margin-right: 10px;
            font-size: 1.2em;
            transition: transform 0.3s;
        }

        .category-header.collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        .category-content {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }

        .category-content.collapsed {
            max-height: 0;
            margin: 0;
        }

        /* Add new pagination styles */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            gap: 10px;
        }

        .pagination button {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .pagination button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .pagination .page-info {
            margin: 0 15px;
            color: #666;
        }

        .loading-indicator {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        /* Fullscreen view styles */
        .fullscreen-view {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #f0f0f0;
            z-index: 2000;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        .fullscreen-header {
            background: white;
            padding: 15px 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 2001;
        }

        .fullscreen-close {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }

        .fullscreen-close:hover {
            background: #c82333;
        }

        /* JSON Structure Explorer Styles */
        .json-explorer {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .json-tree {
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .json-node {
            margin: 5px 0;
            padding: 5px;
            border-left: 2px solid #e9ecef;
            margin-left: 20px;
        }

        .json-node.expandable {
            cursor: pointer;
            border-left-color: #007bff;
        }

        .json-node.expandable:hover {
            background: #f8f9fa;
        }

        .json-key {
            color: #0066cc;
            font-weight: bold;
        }

        .json-type {
            color: #666;
            font-style: italic;
            margin-left: 10px;
        }

        .json-count {
            color: #28a745;
            margin-left: 10px;
        }

        .json-expand-icon {
            display: inline-block;
            width: 16px;
            text-align: center;
            margin-right: 5px;
            transition: transform 0.3s;
        }

        .json-expand-icon.expanded {
            transform: rotate(90deg);
        }

        .json-children {
            display: none;
            margin-left: 20px;
        }

        .json-children.expanded {
            display: block;
        }

        .load-section-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }

        .load-section-btn:hover {
            background: #218838;
        }

        /* Section Cards Styles */
        .sections-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .section-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.15);
            border-color: #007bff;
        }

        .section-icon {
            font-size: 2.5em;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .section-info {
            flex-grow: 1;
        }

        .section-info h4 {
            margin: 0 0 5px 0;
            color: #333;
            font-size: 1.2em;
        }

        .section-info p {
            margin: 0;
            color: #666;
            font-size: 0.9em;
        }

        .section-card .load-section-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: bold;
            transition: background 0.3s ease;
            flex-shrink: 0;
        }

        .section-card .load-section-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="controls">
            <h2>JSON Movies Library</h2>
            <textarea id="jsonInput" placeholder="Paste your JSON here..."></textarea>
            <input type="file" id="fileInput" class="file-input" accept=".json">
            <input type="file" id="largeFileInput" class="file-input" accept=".json">
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">Upload JSON File</button>
            <button class="large-file-btn" onclick="document.getElementById('largeFileInput').click()">📁 Large JSON Files</button>
            <button onclick="loadJSON()">Load JSON</button>
            <button onclick="saveJSON()">Save Changes</button>
            <button id="backToExplorerBtn" onclick="backToJSONExplorer()" style="display:none; background:#6f42c1; color:white;">🔙 العودة للمستكشف</button>
            <div class="progress-container" id="progressContainer">
                <div class="progress-bar" id="progressBar"></div>
                <div class="progress-text" id="progressText">Loading: 0%</div>
            </div>
            <div class="stats" id="stats"></div>
        </div>
        <div id="error" class="error"></div>
        <div id="categoryFilters" class="category-filters"></div>
        <div id="grid" class="grid"></div>
    </div>

    <div class="overlay" id="overlay"></div>
    <div class="edit-form" id="editForm">
        <h3>Edit Movie Details</h3>
        <input type="text" id="editTitle" placeholder="Movie Title">
        <input type="text" id="editImage" placeholder="Image URL">
        <input type="text" id="editLink" placeholder="Watch Link">
        <div class="edit-form-buttons">
            <button onclick="closeEditForm()">Cancel</button>
            <button onclick="saveEditForm()">Save</button>
        </div>
    </div>


    <script>
        let currentData = [];
        let categories = new Set();
        let currentCategory = 'all';
        let editingIndex = -1;
        let categoryData = {}; // Store full category data
        const ITEMS_PER_PAGE = 100;
        let currentPage = 1;
        let loadedCategories = new Set(); // <-- fix: track loaded categories

        // New variables for large JSON handling
        let rawJsonData = null; // Store original JSON structure
        let jsonStructure = {}; // Store analyzed structure
        let isLargeFileMode = false; // Track if we're in large file mode

        // Add debounce function for better performance
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Debounced version of displayMovies
        const debouncedDisplayMovies = debounce(displayMovies, 250);

        // Function to process large complex JSON files
        function processLargeComplexJSON(file) {
            const reader = new FileReader();
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');

            progressContainer.style.display = 'block';
            updateGlobalProgress('تحليل الملف الكبير', 0);

            reader.onprogress = function(e) {
                if (e.lengthComputable) {
                    const percentLoaded = Math.round((e.loaded / e.total) * 50); // First 50% for reading
                    updateGlobalProgress('قراءة الملف', percentLoaded);
                }
            };

            reader.onload = function(e) {
                updateGlobalProgress('تحليل هيكل JSON', 50);

                setTimeout(() => {
                    try {
                        const jsonData = JSON.parse(e.target.result);
                        rawJsonData = jsonData;
                        analyzeJSONStructure(jsonData);
                    } catch (error) {
                        document.getElementById('error').textContent = 'خطأ في تحليل ملف JSON: ' + error.message;
                        progressContainer.style.display = 'none';
                        isLargeFileMode = false;
                    }
                }, 100);
            };

            reader.readAsText(file);
        }

        // Function to analyze JSON structure and find all possible data arrays
        function analyzeJSONStructure(data, path = '', level = 0) {
            updateGlobalProgress('تحليل هيكل البيانات', 60 + (level * 5));

            if (level === 0) {
                jsonStructure = {};
                findMovieSectionsLazy(data);
                displaySimpleSectionsList();
            }
        }

        // Function to find sections containing movie/series data (lazy - no data processing)
        function findMovieSectionsLazy(data, path = '') {
            // Skip settings/config sections
            const skipSections = ['website_info', 'config', 'settings', 'metadata', 'info', 'version', 'api_info', 'site_info'];

            if (Array.isArray(data)) {
                if (data.length > 0) {
                    // Check if this looks like movie/series data (sample only first item)
                    const sample = data[0];
                    const hasMovieFields = sample && (
                        sample.movies_name || sample.series_name || sample.name || sample.title ||
                        sample.movies_img || sample.series_img || sample.img || sample.imageUrl ||
                        sample.movies_href || sample.series_href || sample.href || sample.link
                    );

                    if (hasMovieFields) {
                        // Group by category instead of JSON structure
                        groupDataByCategory(data, path);
                    }
                }
            } else if (typeof data === 'object' && data !== null) {
                for (const key in data) {
                    if (data.hasOwnProperty(key)) {
                        // Skip settings sections
                        if (skipSections.includes(key.toLowerCase())) {
                            continue;
                        }

                        const newPath = path ? `${path}.${key}` : key;
                        findMovieSectionsLazy(data[key], newPath);
                    }
                }
            }
        }

        // Function to group data by category field
        function groupDataByCategory(data, sourcePath) {
            const categoryGroups = {};

            // Group items by their category field
            data.forEach(item => {
                const category = item.category || 'uncategorized';
                if (!categoryGroups[category]) {
                    categoryGroups[category] = [];
                }
                categoryGroups[category].push(item);
            });

            // Create sections for each category
            Object.keys(categoryGroups).forEach(category => {
                const categoryData = categoryGroups[category];
                const categoryPath = `category_${category}`;

                jsonStructure[categoryPath] = {
                    type: 'movies',
                    count: categoryData.length,
                    rawData: categoryData,
                    path: categoryPath,
                    category: category,
                    sourcePath: sourcePath, // Keep reference to original path
                    displayName: formatCategoryName(category),
                    processed: false
                };
            });
        }

        // Function to format section names for display
        function formatSectionName(name) {
            // Convert common English names to Arabic
            const nameMap = {
                'movies': 'الأفلام',
                'series': 'المسلسلات',
                'action_movies': 'أفلام الأكشن',
                'comedy_movies': 'أفلام الكوميديا',
                'drama_movies': 'أفلام الدراما',
                'horror_movies': 'أفلام الرعب',
                'trending_series': 'المسلسلات الرائجة',
                'classic_series': 'المسلسلات الكلاسيكية',
                'documentaries': 'الأفلام الوثائقية',
                'kids_content': 'محتوى الأطفال',
                'animated_movies': 'الأفلام المتحركة',
                'educational': 'المحتوى التعليمي',
                'nature': 'الطبيعة',
                'history': 'التاريخ',
                'series_list': 'قائمة المسلسلات'
            };

            return nameMap[name] || name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }

        // Function to format category names for display
        function formatCategoryName(category) {
            // Convert common category names to Arabic
            const categoryMap = {
                'action': 'أفلام الأكشن',
                'comedy': 'أفلام الكوميديا',
                'drama': 'أفلام الدراما',
                'horror': 'أفلام الرعب',
                'thriller': 'أفلام الإثارة',
                'romance': 'أفلام الرومانسية',
                'sci-fi': 'أفلام الخيال العلمي',
                'science_fiction': 'أفلام الخيال العلمي',
                'fantasy': 'أفلام الفانتازيا',
                'adventure': 'أفلام المغامرة',
                'animation': 'أفلام الرسوم المتحركة',
                'documentary': 'أفلام وثائقية',
                'biography': 'أفلام السيرة الذاتية',
                'history': 'أفلام تاريخية',
                'war': 'أفلام الحرب',
                'western': 'أفلام الغرب الأمريكي',
                'crime': 'أفلام الجريمة',
                'mystery': 'أفلام الغموض',
                'family': 'أفلام العائلة',
                'kids': 'أفلام الأطفال',
                'music': 'أفلام موسيقية',
                'sport': 'أفلام رياضية',
                'series': 'مسلسلات',
                'tv_show': 'برامج تلفزيونية',
                'uncategorized': 'غير مصنف',
                'other': 'أخرى'
            };

            return categoryMap[category.toLowerCase()] || category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }

        // Function to display simple sections list
        function displaySimpleSectionsList() {
            const grid = document.getElementById('grid');
            grid.innerHTML = '';

            // Create sections container
            const sectionsContainer = document.createElement('div');
            sectionsContainer.className = 'json-explorer';
            sectionsContainer.innerHTML = `
                <h3>� أقسام المحتوى المتاحة</h3>
                <p>اختر القسم الذي تريد عرضه:</p>
                <div id="sectionsList" class="sections-list"></div>
            `;
            grid.appendChild(sectionsContainer);

            // Build sections list
            const sectionsList = document.getElementById('sectionsList');
            const sections = Object.values(jsonStructure);

            if (sections.length === 0) {
                sectionsList.innerHTML = `
                    <div style="text-align:center; padding:40px; color:#666;">
                        <h4>❌ لم يتم العثور على أقسام محتوى</h4>
                        <p>الملف لا يحتوي على أقسام تحتوي على أفلام أو مسلسلات</p>
                    </div>
                `;
                updateGlobalProgress('تحليل مكتمل', 100);
                setTimeout(() => {
                    document.getElementById('progressContainer').style.display = 'none';
                }, 1000);
                return;
            }

            sections.forEach((section, index) => {
                const sectionCard = document.createElement('div');
                sectionCard.className = 'section-card';
                sectionCard.id = `section-${section.path.replace(/\./g, '-')}`;

                const isProcessed = section.processed;
                const statusIcon = isProcessed ? '✅' : '⏳';
                const statusText = isProcessed ? 'محمل' : 'غير محمل';
                const buttonText = isProcessed ? 'عرض المحتوى' : 'تحميل وعرض';

                sectionCard.innerHTML = `
                    <div class="section-icon">🎬</div>
                    <div class="section-info">
                        <h4>${section.displayName}</h4>
                        <p>${section.count} عنصر</p>
                        <small style="color: ${isProcessed ? '#28a745' : '#6c757d'};">
                            ${statusIcon} ${statusText}
                        </small>
                    </div>
                    <button class="load-section-btn" onclick="loadJSONSection('${section.path}')">
                        ${buttonText}
                    </button>
                `;
                sectionsList.appendChild(sectionCard);
            });

            // Show statistics
            const statsDiv = document.getElementById('stats');
            const sectionsCount = sections.length;
            const totalItems = sections.reduce((sum, section) => sum + section.count, 0);
            statsDiv.innerHTML = `
                <strong>📊 إحصائيات الملف:</strong>
                تم العثور على <span style="color:#28a745; font-weight:bold;">${sectionsCount}</span> قسم يحتوي على
                <span style="color:#007bff; font-weight:bold;">${totalItems}</span> عنصر إجمالي
            `;

            updateGlobalProgress('تحليل مكتمل', 100);
            setTimeout(() => {
                document.getElementById('progressContainer').style.display = 'none';
            }, 1000);
        }



        // Function to load a specific JSON section (on-demand processing)
        function loadJSONSection(path) {
            if (!rawJsonData || !jsonStructure[path]) {
                showErrorMessage('لا توجد بيانات JSON محملة أو القسم غير موجود');
                return;
            }

            const section = jsonStructure[path];
            const sectionName = section.displayName;

            updateGlobalProgress(`تحميل قسم: ${sectionName}`, 0);

            // Check if section is already processed
            if (section.processed && section.data) {
                updateGlobalProgress('عرض البيانات المحملة مسبقاً', 90);
                displayProcessedSection(section, path);
                return;
            }

            // Get raw data for this section
            const rawData = section.rawData;

            if (!Array.isArray(rawData)) {
                showErrorMessage('البيانات المحددة ليست مصفوفة صالحة');
                return;
            }

            updateGlobalProgress(`معالجة قسم: ${sectionName}`, 10);

            // Process the selected section with chunked processing
            setTimeout(() => {
                try {
                    processSelectedSectionChunked(rawData, path, sectionName);
                } catch (error) {
                    showErrorMessage('خطأ في معالجة البيانات: ' + error.message);
                    document.getElementById('progressContainer').style.display = 'none';
                }
            }, 100);
        }

        // Function to process selected section data with chunked processing
        function processSelectedSectionChunked(rawData, sectionPath, sectionName) {
            const processedData = [];
            const sectionCategories = new Set(['all']);
            const sectionCategoryData = {};

            updateGlobalProgress(`معالجة قسم: ${sectionName}`, 20);

            // Check if data has categories (sample first few items)
            const sampleSize = Math.min(10, rawData.length);
            const hasCategories = rawData.slice(0, sampleSize).some(item => item.category);

            const batchSize = 100; // Larger batch size for better performance
            let processedCount = 0;

            function processBatch(startIndex) {
                const endIndex = Math.min(startIndex + batchSize, rawData.length);

                for (let i = startIndex; i < endIndex; i++) {
                    const item = rawData[i];
                    const movie = {
                        title: item.movies_name || item.series_name || item.name || item.title || 'بدون عنوان',
                        imageUrl: item.movies_img || item.series_img || item.img || item.imageUrl || 'https://via.placeholder.com/200x300',
                        link: item.movies_href || item.series_href || item.href || item.link || '#',
                        category: item.category || 'uncategorized',
                        id: item.id || (window.crypto ? crypto.randomUUID() : Math.random().toString(36).substr(2, 9)),
                        originalData: item // Keep reference to original data
                    };

                    processedData.push(movie);

                    if (hasCategories) {
                        sectionCategories.add(movie.category);
                        if (!sectionCategoryData[movie.category]) {
                            sectionCategoryData[movie.category] = [];
                        }
                        sectionCategoryData[movie.category].push(movie);
                    }
                }

                processedCount = endIndex;
                const progress = 20 + Math.round((processedCount / rawData.length) * 60);
                const percentage = Math.round((processedCount / rawData.length) * 100);
                updateGlobalProgress(`معالجة قسم: ${sectionName} (${processedCount.toLocaleString()}/${rawData.length.toLocaleString()}) - ${percentage}%`, progress);

                if (endIndex < rawData.length) {
                    // Continue processing in next frame
                    setTimeout(() => processBatch(endIndex), 5);
                } else {
                    // Processing complete - store processed data
                    const section = jsonStructure[sectionPath];
                    section.data = processedData;
                    section.categories = sectionCategories;
                    section.categoryData = sectionCategoryData;
                    section.hasCategories = hasCategories;
                    section.processed = true;

                    // Display the processed section
                    displayProcessedSection(section, sectionPath);
                }
            }

            processBatch(0);
        }

        // Function to display processed section
        function displayProcessedSection(section, sectionPath) {
            // Set global data to this section's data
            currentData = section.data;
            categories = section.categories;
            categoryData = section.categoryData;
            loadedCategories = new Set();

            updateGlobalProgress('عرض البيانات', 90);

            // Update statistics
            const statsDiv = document.getElementById('stats');
            if (section.hasCategories) {
                statsDiv.textContent = `القسم: ${section.displayName} | الأفلام: ${currentData.length} | الأقسام: ${categories.size - 1}`;
            } else {
                statsDiv.textContent = `القسم: ${section.displayName} | الأفلام: ${currentData.length}`;
            }

            // Display the data
            if (section.hasCategories) {
                displayMovies();
            } else {
                displayNonCategorizedMovies();
            }

            // Update JSON display
            updateJSONDisplay();

            // Show success message
            showSuccessMessage(`تم تحميل القسم "${section.displayName}" بنجاح (${currentData.length} عنصر)`);

            // Show back to explorer button
            document.getElementById('backToExplorerBtn').style.display = 'inline-block';

            // Update section status in the list (if we go back to explorer)
            updateSectionStatus(sectionPath, true);

            updateGlobalProgress('تم التحميل بنجاح', 100);
            setTimeout(() => {
                document.getElementById('progressContainer').style.display = 'none';
            }, 1000);
        }

        // Function to update section status in the sections list
        function updateSectionStatus(sectionPath, isProcessed) {
            const sectionId = `section-${sectionPath.replace(/\./g, '-')}`;
            const sectionCard = document.getElementById(sectionId);

            if (sectionCard) {
                const statusIcon = isProcessed ? '✅' : '⏳';
                const statusText = isProcessed ? 'محمل' : 'غير محمل';
                const buttonText = isProcessed ? 'عرض المحتوى' : 'تحميل وعرض';

                const statusElement = sectionCard.querySelector('small');
                const buttonElement = sectionCard.querySelector('.load-section-btn');

                if (statusElement) {
                    statusElement.style.color = isProcessed ? '#28a745' : '#6c757d';
                    statusElement.innerHTML = `${statusIcon} ${statusText}`;
                }

                if (buttonElement) {
                    buttonElement.textContent = buttonText;
                }
            }
        }



        // Function to go back to JSON explorer
        function backToJSONExplorer() {
            if (rawJsonData) {
                displaySimpleSectionsList();
                document.getElementById('backToExplorerBtn').style.display = 'none';
            } else {
                showErrorMessage('لا توجد بيانات JSON محملة');
            }
        }

        function showErrorMessage(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.style.color = 'red';
            errorDiv.style.background = 'rgba(220, 53, 69, 0.1)';
            errorDiv.style.border = '1px solid rgba(220, 53, 69, 0.3)';
            errorDiv.textContent = message;

            setTimeout(() => {
                errorDiv.textContent = '';
            }, 5000);
        }

        // Large file input handling
        document.getElementById('largeFileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                isLargeFileMode = true;
                processLargeComplexJSON(file);
            }
        });

        // Modify file input handling with chunked processing
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                const progressContainer = document.getElementById('progressContainer');
                const progressBar = document.getElementById('progressBar');
                const progressText = document.getElementById('progressText');

                progressContainer.style.display = 'block';
                updateGlobalProgress('تحميل الملف', 0);

                reader.onprogress = function(e) {
                    if (e.lengthComputable) {
                        const percentLoaded = Math.round((e.loaded / e.total) * 100);
                        updateGlobalProgress('تحميل الملف', percentLoaded);
                    }
                };

                reader.onload = function(e) {
                    updateGlobalProgress('معالجة البيانات', 100);

                    // Process the JSON in the next frame to prevent UI blocking
                    setTimeout(() => {
                        try {
                            const jsonData = JSON.parse(e.target.result);
                            processLargeDataset(jsonData, progressText, progressBar, progressContainer);
                        } catch (error) {
                            document.getElementById('error').textContent = 'Invalid JSON format: ' + error.message;
                            progressContainer.style.display = 'none';
                        }
                    }, 0);
                };

                reader.readAsText(file);
            }
        });

        // Modify processLargeDataset function for progressive loading
        function processLargeDataset(data, progressText, progressBar, progressContainer) {
            let movies = [];
            categories = new Set(['all']);
            categoryData = {};

            // Determine the data structure
            if (Array.isArray(data)) {
                movies = data;
            } else {
                const possibleArrays = ['movies', 'movies_info', 'series'];
                for (const key of possibleArrays) {
                    if (Array.isArray(data[key])) {
                        movies = data[key];
                        break;
                    }
                }
            }

            if (!movies.length) {
                throw new Error('No movie data found in the JSON structure');
            }

            // First phase: Check if movies have categories
            progressText.textContent = 'Processing data...';
            updateGlobalProgress('تحليل البيانات', 10);
            const totalItems = movies.length;
            const hasCategories = movies.some(movie => movie.category);


            loadedCategories = new Set(); // reset loaded categories on new load
            if (!hasCategories) {
                // Handle non-categorized movies directly
                processNonCategorizedMovies(movies, progressText, progressBar, progressContainer);
            } else {
                // Continue with categorized processing as before
                extractCategories(0, 100);
            }

            function processNonCategorizedMovies(movies, progressText, progressBar, progressContainer) {
                currentData = [];
                const batchSize = 100;

                function processBatch(startIndex) {
                    const endIndex = Math.min(startIndex + batchSize, movies.length);

                    for (let i = startIndex; i < endIndex; i++) {
                        const movie = movies[i];
                        currentData.push({
                            title: movie.movies_name || movie.series_name || movie.name || movie.title || 'بدون عنوان',
                            imageUrl: movie.movies_img || movie.series_img || movie.img || movie.imageUrl || 'https://via.placeholder.com/200x300',
                            link: movie.movies_href || movie.series_href || movie.href || movie.link || '#',
                            id: movie.id || (window.crypto ? crypto.randomUUID() : Math.random().toString(36).substr(2, 9))
                        });
                    }

                    const progress = Math.round((endIndex / movies.length) * 80) + 10;
                    updateGlobalProgress('معالجة البطاقات', progress);

                    if (endIndex < movies.length) {
                        setTimeout(() => processBatch(endIndex), 0);
                    } else {
                        // Complete processing
                        progressText.textContent = 'Processing complete!';
                        document.getElementById('stats').textContent =
                            `Total movies: ${currentData.length}`;
                        displayNonCategorizedMovies();
                        setTimeout(() => {
                            progressContainer.style.display = 'none';
                            document.getElementById('jsonInput').value = JSON.stringify({ movies: currentData }, null, 2);
                        }, 1000);
                    }
                }

                processBatch(0);
            }

            function extractCategories(startIndex, batchSize) {
                const endIndex = Math.min(startIndex + batchSize, totalItems);

                for (let i = startIndex; i < endIndex; i++) {
                    const category = movies[i].category || 'uncategorized';
                    categories.add(category);
                }

                const progress = Math.round((endIndex / totalItems) * 50); // First phase uses 0-50%
                progressBar.style.width = progress + '%';
                progressText.textContent = `Phase 1: Extracting categories... ${progress}%`;

                if (endIndex < totalItems) {
                    setTimeout(() => extractCategories(endIndex, batchSize), 0);
                } else {
                    // Start second phase: Processing movies into categories
                    processMoviesIntoCategories(0, 50);
                }
            }

            function processMoviesIntoCategories(startIndex, batchSize) {
                const endIndex = Math.min(startIndex + batchSize, totalItems);

                for (let i = startIndex; i < endIndex; i++) {
                    const movie = movies[i];
                    const category = movie.category || 'uncategorized';
                    const item = {
                        title: movie.movies_name || movie.series_name || movie.name || movie.title || 'بدون عنوان',
                        imageUrl: movie.movies_img || movie.series_img || movie.img || movie.imageUrl || 'https://via.placeholder.com/200x300',
                        link: movie.movies_href || movie.series_href || movie.href || movie.link || '#',
                        category: category,
                        id: movie.id || (window.crypto ? crypto.randomUUID() : Math.random().toString(36).substr(2, 9))
                    };

                    if (!categoryData[category]) {
                        categoryData[category] = [];
                    }
                    categoryData[category].push(item);
                    currentData.push(item);
                }

                // Calculate progress for second phase (50-100%)
                const progress = 50 + Math.round((endIndex / totalItems) * 50);
                progressBar.style.width = progress + '%';
                progressText.textContent = `Phase 2: Processing movies... ${progress}%`;

                if (endIndex < totalItems) {
                    // Continue processing
                    setTimeout(() => processMoviesIntoCategories(endIndex, batchSize), 0);
                } else {
                    // Complete processing
                    progressText.textContent = 'Processing complete!';
                    document.getElementById('stats').textContent =
                        `Total movies: ${currentData.length} | Categories: ${categories.size - 1}`;
                    displayMovies();
                    setTimeout(() => {
                        progressContainer.style.display = 'none';
                        document.getElementById('jsonInput').value = JSON.stringify({ movies: currentData }, null, 2);
                    }, 1000);
                }
            }

            // Start the process
            currentData = []; // Reset current data
            setTimeout(() => extractCategories(0, 100), 0);
        }

        function loadJSON() {
            const jsonInput = document.getElementById('jsonInput');
            const errorDiv = document.getElementById('error');
            const statsDiv = document.getElementById('stats');

            if (!jsonInput.value.trim()) {
                errorDiv.textContent = 'Please enter JSON data or upload a JSON file';
                return;
            }

            try {
                const data = JSON.parse(jsonInput.value);
                let movies = [];
                categories = new Set(['all']);
                categoryData = {};
                loadedCategories = new Set();

                // Handle different JSON structures
                if (Array.isArray(data)) {
                    movies = data;
                } else {
                    // Check for various possible properties that might contain movie arrays
                    const possibleArrays = ['movies', 'movies_info', 'series'];
                    for (const key of possibleArrays) {
                        if (Array.isArray(data[key])) {
                            movies = data[key];
                            break;
                        }
                    }
                }

                if (!movies.length) {
                    errorDiv.textContent = 'لم يتم العثور على بيانات أفلام في ملف JSON';
                    return;
                }

                // Check if movies have categories
                const hasCategories = movies.some(movie => movie.category);
                if (!hasCategories) {
                    // Non-categorized
                    currentData = movies.map(movie => ({
                        title: movie.movies_name || movie.series_name || movie.name || movie.title || 'بدون عنوان',
                        imageUrl: movie.movies_img || movie.series_img || movie.img || movie.imageUrl || 'https://via.placeholder.com/200x300',
                        link: movie.movies_href || movie.series_href || movie.href || movie.link || '#',
                        id: movie.id || (window.crypto ? crypto.randomUUID() : Math.random().toString(36).substr(2, 9))
                    }));
                    statsDiv.textContent = `Total movies: ${currentData.length}`;
                    displayNonCategorizedMovies();
                } else {
                    // Categorized
                    currentData = [];
                    for (const movie of movies) {
                        const category = movie.category || 'uncategorized';
                        categories.add(category);
                        const item = {
                            title: movie.movies_name || movie.series_name || movie.name || movie.title || 'بدون عنوان',
                            imageUrl: movie.movies_img || movie.series_img || movie.img || movie.imageUrl || 'https://via.placeholder.com/200x300',
                            link: movie.movies_href || movie.series_href || movie.href || movie.link || '#',
                            category: category,
                            id: movie.id || (window.crypto ? crypto.randomUUID() : Math.random().toString(36).substr(2, 9))
                        };
                        if (!categoryData[category]) categoryData[category] = [];
                        categoryData[category].push(item);
                        currentData.push(item);
                    }
                    statsDiv.textContent = `Total movies: ${currentData.length} | Categories: ${categories.size}`;
                    displayMovies();
                }
                errorDiv.textContent = '';
            } catch (error) {
                errorDiv.textContent = 'Invalid JSON format: ' + error.message;
            }
        }

        // Modify displayMovies function to implement lazy loading
        function displayMovies() {
            const grid = document.getElementById('grid');
            grid.innerHTML = '';
            updateGlobalProgress('جاري تجهيز وعرض البطاقات...', 95);

            // Create summary statistics
            const summaryStats = document.createElement('div');
            summaryStats.className = 'summary-stats';
            summaryStats.innerHTML = `
                <h3>Library Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-item">Total Movies: ${currentData.length}</div>
                    <div class="stat-item">Categories: ${categories.size - 1}</div>
                </div>
            `;
            grid.appendChild(summaryStats);

            // Group movies by category (no fragment, direct append for correct event binding)
            // Virtualized chunked rendering for very large category lists
            const categoriesArr = Object.entries(categoryData);
            const CHUNK_SIZE = 10;
            let index = 0;
            function renderChunk() {
                const end = Math.min(index + CHUNK_SIZE, categoriesArr.length);
                for (let i = index; i < end; i++) {
                    const [category, movies] = categoriesArr[i];
                    const section = document.createElement('div');
                    section.className = 'category-section';
                    section.setAttribute('data-category', category);

                    const header = document.createElement('div');
                    header.className = 'category-header collapsed';
                    header.innerHTML = `
                        <span class="toggle-icon">▼</span>
                        <h2>${category.charAt(0).toUpperCase() + category.slice(1)}</h2>
                        <span class="movie-count">(${movies.length} movies)</span>
                    `;

                    const content = document.createElement('div');
                    content.className = 'category-content collapsed';

                    section.appendChild(header);
                    section.appendChild(content);
                    grid.appendChild(section);

                    // Add click handler for opening in a new tab
                    header.addEventListener('click', (e) => {
                        e.preventDefault();
                        displayCategoryContent(category); // فتح القسم في نفس الصفحة
                    });
                }
                index = end;
                if (index < categoriesArr.length) {
                    setTimeout(renderChunk, 15);
                } else {
                    updateGlobalProgress('تم عرض جميع البطاقات', 100);
                }
            }
            renderChunk();
        }

        function displayCategoryContent(category) {
            // Remove any previous fullscreen view
            const oldView = document.getElementById('fullscreenView');
            if (oldView) oldView.remove();

            // Create fullscreen overlay
            const fullscreen = document.createElement('div');
            fullscreen.className = 'fullscreen-view';
            fullscreen.id = 'fullscreenView';

            // Header with close button
            const header = document.createElement('div');
            header.className = 'fullscreen-header';
            header.innerHTML = `<span><b>القسم:</b> ${category} (عدد: ${categoryData[category].length})</span>`;
            const closeBtn = document.createElement('button');
            closeBtn.className = 'fullscreen-close';
            closeBtn.textContent = 'إغلاق';
            closeBtn.onclick = () => fullscreen.remove();
            header.appendChild(closeBtn);
            fullscreen.appendChild(header);

            // Progress indicator
            const progressDiv = document.createElement('div');
            progressDiv.className = 'loading-indicator';
            progressDiv.id = 'categoryLoadingIndicator';
            progressDiv.textContent = 'جاري تحميل الأفلام...';
            fullscreen.appendChild(progressDiv);

            // Movies grid
            const moviesContainer = document.createElement('div');
            moviesContainer.className = 'grid';
            moviesContainer.style.flex = '1';
            fullscreen.appendChild(moviesContainer);

            // Pagination will be added below
            const paginationDiv = document.createElement('div');
            paginationDiv.id = 'fullscreenPagination';
            fullscreen.appendChild(paginationDiv);

            document.body.appendChild(fullscreen);

            // Virtualized rendering for large categories with progress
            function displayCategoryPageVirtualized(category, page, container, paginationDiv, progressDiv) {
                container.innerHTML = '';
                const movies = categoryData[category];
                const totalPages = Math.ceil(movies.length / ITEMS_PER_PAGE);
                const start = (page - 1) * ITEMS_PER_PAGE;
                const end = Math.min(start + ITEMS_PER_PAGE, movies.length);
                const pageMovies = movies.slice(start, end);

                // Render cards in small chunks to avoid UI freeze
                let cardIndex = 0;
                function renderCardsChunk() {
                    const chunkSize = 5;
                    const chunkEnd = Math.min(cardIndex + chunkSize, pageMovies.length);
                    for (let i = cardIndex; i < chunkEnd; i++) {
                        const movie = pageMovies[i];
                        const globalIndex = currentData.indexOf(movie) + 1; // رقم الفيلم في كل البيانات
                        const card = document.createElement('div');
                        card.className = 'card';
                        card.innerHTML = `
                            <div style="position:absolute;right:8px;top:8px;background:#007bff;color:#fff;padding:2px 8px;border-radius:12px;font-size:1em;z-index:2;">${globalIndex}</div>
                            <img src="${movie.imageUrl}" alt="${movie.title}" onerror="this.src='https://via.placeholder.com/200x300'">
                            <div class="card-content">
                                <h3 class="card-title">${movie.title}</h3>
                                <div class="card-buttons">
                                    <button class="delete-btn" onclick="deleteMovie(${currentData.indexOf(movie)})" title="حذف الفيلم">🗑️</button>
                                    <button class="edit-btn" onclick="openEditForm(${currentData.indexOf(movie)})">Edit</button>
                                    <a href="${movie.link}" class="card-link" target="_blank">Watch</a>
                                </div>
                            </div>
                        `;
                        container.appendChild(card);
                    }
                    cardIndex = chunkEnd;
                    // Update progress
                    if (progressDiv) {
                        const percent = Math.round((cardIndex / pageMovies.length) * 100);
                        updateGlobalProgress('عرض بطاقات القسم', percent);
                    }
                    if (cardIndex < pageMovies.length) {
                        setTimeout(renderCardsChunk, 15); // تأخير بسيط لإعطاء فرصة للمتصفح
                    } else {
                        if (progressDiv) progressDiv.style.display = 'none';
                    }
                }
                renderCardsChunk();

                // Create improved pagination
                paginationDiv.innerHTML = '';
                paginationDiv.className = 'pagination';

                const prevButton = document.createElement('button');
                prevButton.textContent = '← السابق';
                prevButton.disabled = page === 1;
                prevButton.onclick = () => displayCategoryPageVirtualized(category, page - 1, container, paginationDiv, progressDiv);

                const nextButton = document.createElement('button');
                nextButton.textContent = 'التالي →';
                nextButton.disabled = page === totalPages;
                nextButton.onclick = () => displayCategoryPageVirtualized(category, page + 1, container, paginationDiv, progressDiv);

                // أزرار أرقام الصفحات (10 صفحات تالية)
                let startPage = Math.max(1, page - 4);
                let endPage = Math.min(totalPages, startPage + 9);
                if (endPage - startPage < 9) {
                    startPage = Math.max(1, endPage - 9);
                }

                paginationDiv.appendChild(prevButton);

                for (let i = startPage; i <= endPage; i++) {
                    const pageBtn = document.createElement('button');
                    pageBtn.textContent = i;
                    if (i === page) {
                        pageBtn.disabled = true;
                        pageBtn.style.background = '#0056b3';
                    }
                    pageBtn.onclick = () => displayCategoryPageVirtualized(category, i, container, paginationDiv, progressDiv);
                    paginationDiv.appendChild(pageBtn);
                }

                paginationDiv.appendChild(nextButton);
            }

            // Use setTimeout to avoid blocking UI for large categories
            setTimeout(() => displayCategoryPageVirtualized(category, 1, moviesContainer, paginationDiv, progressDiv), 0);
        }

        // 1. أضف هذا الكود بعد تعريف المتغيرات العامة مباشرة (خارج أي دالة):
        window.addEventListener('storage', function(e) {
            if (e.key === 'openCategory' && e.newValue) {
                const cat = e.newValue;
                localStorage.removeItem('openCategory');
                setTimeout(() => {
                    if (categoryData[cat]) {
                        displayCategoryContent(cat);
                    }
                }, 200);
            }
        });

        function displayCategoryPage(category, page, container, paginationDiv) {
            container.innerHTML = '';
            const movies = categoryData[category];
            const start = (page - 1) * ITEMS_PER_PAGE;
            const end = start + ITEMS_PER_PAGE;
            const pageMovies = movies.slice(start, end);

            pageMovies.forEach(movie => {
                const globalIndex = currentData.indexOf(movie) + 1;
                const card = document.createElement('div');
                card.className = 'card';
                card.innerHTML = `
                    <div style="position:absolute;right:8px;top:8px;background:#007bff;color:#fff;padding:2px 8px;border-radius:12px;font-size:1em;z-index:2;">${globalIndex}</div>
                    <img src="${movie.imageUrl}" alt="${movie.title}" onerror="this.src='https://via.placeholder.com/200x300'">
                    <div class="card-content">
                        <h3 class="card-title">${movie.title}</h3>
                        <div class="card-buttons">
                            <button class="delete-btn" onclick="deleteMovie(${currentData.indexOf(movie)})" title="حذف الفيلم">🗑️</button>
                            <button class="edit-btn" onclick="openEditForm(${currentData.indexOf(movie)})">Edit</button>
                            <a href="${movie.link}" class="card-link" target="_blank">Watch</a>
                        </div>
                    </div>
                `;
                container.appendChild(card);
            });

            // Create improved pagination
            const totalPages = Math.ceil(movies.length / ITEMS_PER_PAGE);
            paginationDiv.innerHTML = '';
            paginationDiv.className = 'pagination';

            const prevButton = document.createElement('button');
            prevButton.textContent = '← السابق';
            prevButton.disabled = page === 1;
            prevButton.onclick = () => displayCategoryPage(category, page - 1, container, paginationDiv);

            const nextButton = document.createElement('button');
            nextButton.textContent = 'التالي →';
            nextButton.disabled = page === totalPages;
            nextButton.onclick = () => displayCategoryPage(category, page + 1, container, paginationDiv);

            // أزرار أرقام الصفحات (10 صفحات تالية)
            let startPage = Math.max(1, page - 4);
            let endPage = Math.min(totalPages, startPage + 9);
            if (endPage - startPage < 9) {
                startPage = Math.max(1, endPage - 9);
            }

            paginationDiv.appendChild(prevButton);

            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                if (i === page) {
                    pageBtn.disabled = true;
                    pageBtn.style.background = '#0056b3';
                }
                pageBtn.onclick = () => displayCategoryPage(category, i, container, paginationDiv);
                paginationDiv.appendChild(pageBtn);
            }

            paginationDiv.appendChild(nextButton);
        }

        function createPagination(container, totalItems, currentPage, category) {
            const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);
            const paginationDiv = document.createElement('div');
            paginationDiv.className = 'pagination';

            const prevButton = document.createElement('button');
            prevButton.textContent = '← Previous';
            prevButton.disabled = currentPage === 1;
            prevButton.onclick = () => changePage(category, currentPage - 1);

            const nextButton = document.createElement('button');
            nextButton.textContent = 'Next →';
            nextButton.disabled = currentPage === totalPages;
            nextButton.onclick = () => changePage(category, currentPage + 1);

            const pageInfo = document.createElement('span');
            pageInfo.className = 'page-info';
            pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;

            paginationDiv.appendChild(prevButton);
            paginationDiv.appendChild(pageInfo);
            paginationDiv.appendChild(nextButton);
            container.appendChild(paginationDiv);
        }

        function changePage(category, newPage) {
            currentPage = newPage;
            displayCategoryContent(category);
        }

        function openEditForm(index) {
            editingIndex = index;
            const movie = currentData[index];

            document.getElementById('editTitle').value = movie.title;
            document.getElementById('editImage').value = movie.imageUrl;
            document.getElementById('editLink').value = movie.link;

            document.getElementById('overlay').style.display = 'block';
            document.getElementById('editForm').style.display = 'block';
        }

        function closeEditForm() {
            document.getElementById('overlay').style.display = 'none';
            document.getElementById('editForm').style.display = 'none';
            editingIndex = -1;
        }

        function saveEditForm() {
            if (editingIndex === -1) return;

            const movie = currentData[editingIndex];
            currentData[editingIndex] = {
                title: document.getElementById('editTitle').value,
                imageUrl: document.getElementById('editImage').value,
                link: document.getElementById('editLink').value,
                category: movie.category, // الحفاظ على القسم
                id: movie.id // الحفاظ على المعرف
            };

            // تحديث البيانات في categoryData إذا كانت موجودة
            if (movie.category && categoryData[movie.category]) {
                const categoryIndex = categoryData[movie.category].findIndex(item => item.id === movie.id);
                if (categoryIndex !== -1) {
                    categoryData[movie.category][categoryIndex] = currentData[editingIndex];
                }
            }

            displayMovies();
            closeEditForm();
        }

        function deleteMovie(index) {
            if (index < 0 || index >= currentData.length) return;

            const movie = currentData[index];
            const movieTitle = movie.title;

            // تأكيد الحذف
            if (!confirm(`هل أنت متأكد من حذف الفيلم "${movieTitle}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                return;
            }

            // حذف من currentData
            currentData.splice(index, 1);

            // حذف من categoryData إذا كانت البيانات مصنفة
            if (movie.category && categoryData[movie.category]) {
                const categoryIndex = categoryData[movie.category].findIndex(item => item.id === movie.id);
                if (categoryIndex !== -1) {
                    categoryData[movie.category].splice(categoryIndex, 1);

                    // إذا أصبح القسم فارغاً، احذفه
                    if (categoryData[movie.category].length === 0) {
                        delete categoryData[movie.category];
                        categories.delete(movie.category);
                    }
                }
            }

            // تحديث الإحصائيات
            const statsDiv = document.getElementById('stats');
            if (Object.keys(categoryData).length > 0) {
                statsDiv.textContent = `Total movies: ${currentData.length} | Categories: ${Object.keys(categoryData).length}`;
            } else {
                statsDiv.textContent = `Total movies: ${currentData.length}`;
            }

            // إعادة عرض البيانات
            if (Object.keys(categoryData).length > 0) {
                displayMovies();
            } else {
                displayNonCategorizedMovies();
            }

            // تحديث JSON في النص
            updateJSONDisplay();

            // إظهار رسالة نجاح
            showSuccessMessage(`تم حذف الفيلم "${movieTitle}" بنجاح`);
        }

        function showSuccessMessage(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.style.color = 'green';
            errorDiv.style.background = 'rgba(40, 167, 69, 0.1)';
            errorDiv.style.border = '1px solid rgba(40, 167, 69, 0.3)';
            errorDiv.textContent = message;

            // إخفاء الرسالة بعد 3 ثوان
            setTimeout(() => {
                errorDiv.textContent = '';
                errorDiv.style.color = 'red';
                errorDiv.style.background = 'rgba(220, 53, 69, 0.1)';
                errorDiv.style.border = '1px solid rgba(220, 53, 69, 0.3)';
            }, 3000);
        }

        function updateJSONDisplay() {
            const output = {
                movies: currentData.map(movie => ({
                    id: movie.id,
                    name: movie.title,
                    img: movie.imageUrl,
                    href: movie.link,
                    category: movie.category,
                    addedDate: new Date().toISOString(),
                    hidden: false
                }))
            };

            document.getElementById('jsonInput').value = JSON.stringify(output, null, 2);
        }

        function saveJSON() {
            const output = {
                movies: currentData.map(movie => ({
                    id: movie.id,
                    name: movie.title,
                    img: movie.imageUrl,
                    href: movie.link,
                    category: movie.category,
                    addedDate: new Date().toISOString(),
                    hidden: false
                }))
            };

            document.getElementById('jsonInput').value = JSON.stringify(output, null, 2);
        }

        function displayNonCategorizedMovies() {
            const grid = document.getElementById('grid');
            grid.innerHTML = '';
            updateGlobalProgress('جاري تجهيز وعرض البطاقات...', 95);

            // Create summary statistics
            const summaryStats = document.createElement('div');
            summaryStats.className = 'summary-stats';
            summaryStats.innerHTML = `
                <h3>Library Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-item">Total Movies: ${currentData.length}</div>
                </div>
            `;
            grid.appendChild(summaryStats);

            // Create movies container (full page grid)
            const moviesContainer = document.createElement('div');
            moviesContainer.className = 'grid'; // Use the main grid style for full page
            grid.appendChild(moviesContainer);

            // Display first page
            displayNonCategorizedPage(1, moviesContainer);
        }

        function displayNonCategorizedPage(page, container) {
            container.innerHTML = '';
            const start = (page - 1) * ITEMS_PER_PAGE;
            const end = Math.min(start + ITEMS_PER_PAGE, currentData.length);
            const pageMovies = currentData.slice(start, end);

            pageMovies.forEach(movie => {
                const globalIndex = currentData.indexOf(movie) + 1;
                const card = document.createElement('div');
                card.className = 'card';
                card.innerHTML = `
                    <div style="position:absolute;right:8px;top:8px;background:#007bff;color:#fff;padding:2px 8px;border-radius:12px;font-size:1em;z-index:2;">${globalIndex}</div>
                    <img src="${movie.imageUrl}" alt="${movie.title}" onerror="this.src='https://via.placeholder.com/200x300'">
                    <div class="card-content">
                        <h3 class="card-title">${movie.title}</h3>
                        <div class="card-buttons">
                            <button class="delete-btn" onclick="deleteMovie(${currentData.indexOf(movie)})" title="حذف الفيلم">🗑️</button>
                            <button class="edit-btn" onclick="openEditForm(${currentData.indexOf(movie)})">Edit</button>
                            <a href="${movie.link}" class="card-link" target="_blank">Watch</a>
                        </div>
                    </div>
                `;
                container.appendChild(card);
            });

            // Create improved pagination
            const totalPages = Math.ceil(currentData.length / ITEMS_PER_PAGE);
            const paginationDiv = document.createElement('div');
            paginationDiv.className = 'pagination';

            const prevButton = document.createElement('button');
            prevButton.textContent = '← السابق';
            prevButton.disabled = page === 1;
            prevButton.onclick = () => displayNonCategorizedPage(page - 1, container);

            const nextButton = document.createElement('button');
            nextButton.textContent = 'التالي →';
            nextButton.disabled = page === totalPages;
            nextButton.onclick = () => displayNonCategorizedPage(page + 1, container);

            // أزرار أرقام الصفحات (10 صفحات تالية)
            let startPage = Math.max(1, page - 4);
            let endPage = Math.min(totalPages, startPage + 9);
            if (endPage - startPage < 9) {
                startPage = Math.max(1, endPage - 9);
            }

            paginationDiv.appendChild(prevButton);

            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                if (i === page) {
                    pageBtn.disabled = true;
                    pageBtn.style.background = '#0056b3';
                }
                pageBtn.onclick = () => displayNonCategorizedPage(i, container);
                paginationDiv.appendChild(pageBtn);
            }

            paginationDiv.appendChild(nextButton);

            // ضع الباجيناشن أسفل الشبكة وليس داخلها
            container.parentElement.appendChild(paginationDiv);

            // Update progress
            if (end >= currentData.length) {
                updateGlobalProgress('تم عرض جميع البطاقات', 100);
            }
        }

        // Example JSON to show format
        document.getElementById('jsonInput').value = JSON.stringify({
            "movies_info": [
                {
                    "movies_name": "Example Movie",
                    "movies_img": "https://example.com/image.jpg",
                    "movies_href": "https://example.com/watch"
                }
            ]
        }, null, 2);

        // ضع هذا التعريف في أعلى السكريبت بعد المتغيرات العامة مباشرة:
        function updateGlobalProgress(text, percent) {
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            progressContainer.style.display = 'block';
            progressBar.style.width = percent + '%';
            progressText.textContent = text + ' ' + percent + '%';
            if (percent >= 100) {
                setTimeout(() => { progressContainer.style.display = 'none'; }, 800);
            }
        }
    </script>
</body>
</html>
