﻿{
  "movies_info": [
    {
      "movies_name": "فيلم مع رموز غريبة ���",
      "movies_img": "https://via.placeholder.com/200x300",
      "movies_href": "https://example.com/movie1"
    },
    {
      movies_name: فيلم بدون اقتباسات نهائياً,
      movies_img: https://via.placeholder.com/200x300,
      movies_href: https://example.com/movie2
    },
    {
      "movies_name": "فيلم عادي",
      "movies_img": "https://via.placeholder.com/200x300",
      "movies_href": "https://example.com/movie3"
    }
    // تعليق غير صالح في JSON
    {
      "movies_name": "فيلم آخر",
      "movies_img": "https://via.placeholder.com/200x300",
      "movies_href": "https://example.com/movie4",
      "category": "دراما"
    },
    {
      "movies_name": "فيلم مع فاصلة زائدة",
      "movies_img": "https://via.placeholder.com/200x300",
      "movies_href": "https://example.com/movie5",,,
    },
    undefined,
    {
      "movies_name": "فيلم أخير",
      "movies_img": "https://via.placeholder.com/200x300",
      "movies_href": "https://example.com/movie6"
    }
  ]
  "invalid_section": broken json here
}
