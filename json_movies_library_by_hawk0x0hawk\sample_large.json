{"website_info": {"name": "مكتبة الأفلام الكبيرة", "version": "2.0", "last_updated": "2024-01-15"}, "all_movies": [{"id": 1, "movies_name": "فيلم الأكشن الأول", "movies_img": "https://via.placeholder.com/300x400/ff6b6b/ffffff?text=Action+1", "movies_href": "https://example.com/action1", "category": "action", "year": 2023, "rating": 8.5}, {"id": 2, "movies_name": "معركة الأبطال", "movies_img": "https://via.placeholder.com/300x400/4ecdc4/ffffff?text=Heroes", "movies_href": "https://example.com/heroes", "category": "action", "year": 2023, "rating": 9.0}, {"id": 3, "movies_name": "المهمة المستحيلة", "movies_img": "https://via.placeholder.com/300x400/45b7d1/ffffff?text=Mission", "movies_href": "https://example.com/mission", "category": "action", "year": 2024, "rating": 8.8}, {"id": 4, "movies_name": "كوميديا رائعة", "movies_img": "https://via.placeholder.com/300x400/f9ca24/ffffff?text=Comedy+1", "movies_href": "https://example.com/comedy1", "category": "comedy", "year": 2023, "rating": 7.5}, {"id": 5, "movies_name": "<PERSON><PERSON><PERSON> بلا حدود", "movies_img": "https://via.placeholder.com/300x400/f0932b/ffffff?text=Laugh", "movies_href": "https://example.com/laugh", "category": "comedy", "year": 2024, "rating": 8.2}, {"id": 6, "movies_name": "قصة حياة", "movies_img": "https://via.placeholder.com/300x400/6c5ce7/ffffff?text=Life+Story", "movies_href": "https://example.com/life", "category": "drama", "year": 2023, "rating": 9.2}, {"id": 7, "movies_name": "الحب الأبدي", "movies_img": "https://via.placeholder.com/300x400/a55eea/ffffff?text=Love", "movies_href": "https://example.com/love", "category": "drama", "year": 2024, "rating": 8.7}, {"id": 8, "series_name": "مسلسل الإثارة", "series_img": "https://via.placeholder.com/300x400/26de81/ffffff?text=Thriller+Series", "series_href": "https://example.com/thriller-series", "category": "thriller", "seasons": 3, "episodes": 24}, {"id": 9, "series_name": "عائلة المستقبل", "series_img": "https://via.placeholder.com/300x400/fd79a8/ffffff?text=Future+Family", "series_href": "https://example.com/future-family", "category": "sci-fi", "seasons": 2, "episodes": 16}, {"id": 10, "series_name": "الذكريات الجميلة", "series_img": "https://via.placeholder.com/300x400/fdcb6e/ffffff?text=Memories", "series_href": "https://example.com/memories", "category": "classic", "seasons": 5, "episodes": 50}, {"id": 11, "name": "عجائب الطبيعة", "img": "https://via.placeholder.com/300x400/00b894/ffffff?text=Nature", "link": "https://example.com/nature", "category": "documentary", "duration": 120}, {"id": 12, "name": "تاريخ الحضارات", "img": "https://via.placeholder.com/300x400/e17055/ffffff?text=History", "link": "https://example.com/history", "category": "documentary", "duration": 90}, {"id": 13, "title": "مغامرة الأطفال", "imageUrl": "https://via.placeholder.com/300x400/74b9ff/ffffff?text=Kids+Adventure", "link": "https://example.com/kids-adventure", "category": "kids", "age_rating": "G"}, {"id": 14, "title": "تعلم مع الأصدقاء", "imageUrl": "https://via.placeholder.com/300x400/81ecec/ffffff?text=Learn+Friends", "link": "https://example.com/learn-friends", "category": "kids", "age_rating": "G"}, {"id": 15, "movies_name": "فيلم رعب مخيف", "movies_img": "https://via.placeholder.com/300x400/2d3436/ffffff?text=Horror", "movies_href": "https://example.com/horror", "category": "horror", "year": 2024, "rating": 7.8}, {"id": 16, "movies_name": "مغامرة في الفضاء", "movies_img": "https://via.placeholder.com/300x400/0984e3/ffffff?text=Space", "movies_href": "https://example.com/space", "category": "sci-fi", "year": 2024, "rating": 8.9}]}